{"name": "admesh-ui-sdk", "version": "0.8.1", "description": "Beautiful, modern React components for displaying AI-powered product recommendations with citation-based conversation ads, auto-triggered widgets, floating chat, conversational interfaces, persistent sidebar, and built-in tracking", "type": "module", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "files": ["dist", "README.md", "INTEGRATION_GUIDE.md", "CONVERSATIONAL_GUIDE.md", "USAGE.md", "examples"], "scripts": {"dev": "vite", "build": "vite build", "build:types": "tsc --emitDeclarationOnly", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "npx chromatic --exit-zero-on-changes", "deploy:storybook": "npm run build-storybook && npx chromatic --storybook-build-dir=storybook-static", "prepublishOnly": "npm run build"}, "keywords": ["admesh", "ui", "components", "recommendations", "tracking", "react", "typescript", "tailwind", "tailwindcss", "ai", "product-recommendations", "modern-ui", "design-system", "cards", "comparison-table", "dark-mode", "responsive", "conversational", "chat", "ai-assistant", "inline-ads", "conversation-summary", "sidebar", "persistent-panel", "recommendation-sidebar", "side-panel", "floating-chat", "chat-widget", "chat-interface", "ai-chat", "conversational-ui", "auto-recommendations", "ai-integration", "chatgpt-plugin", "claude-integration", "auto-trigger", "citation", "citations", "citation-based", "academic-style", "references", "inline-citations", "numbered-citations", "bracketed-citations", "superscript-citations", "clickable-citations", "product-citations"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/GouniManikumar12/admesh-ui-sdk.git"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "dependencies": {"classnames": "^2.5.1"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.0", "@eslint/js": "^9.25.0", "@storybook/addon-a11y": "^9.0.4", "@storybook/addon-docs": "^9.0.4", "@storybook/addon-onboarding": "^9.0.4", "@storybook/addon-vitest": "^9.0.4", "@storybook/react-vite": "^9.0.4", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "^3.2.0", "@vitest/coverage-v8": "^3.2.0", "autoprefixer": "^10.4.21", "clsx": "^2.0.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "^9.0.4", "globals": "^16.0.0", "playwright": "^1.52.0", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "storybook": "^9.0.4", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.0"}}